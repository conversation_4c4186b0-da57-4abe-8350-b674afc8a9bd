package com.talk51.modules.leads.dto;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

public class PopularityStatisticalDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 企业微信机器人编号
     */
    private String enterpriseId;

    /**
     * 用户编号
     */
    private Long userImId;

    /**
     * 流量唯一编号
     */
    private String uniqId;

    /**
     * 完整流量来源文本
     */
    private String originalText;

    /**
     * 消息渠道（XHS--小红书 DY--抖音 WX_VIDEO--微信视频号）
     */
    private String channel;

    /**
     * 流量完整链接
     */
    private String link;

    /**
     * 流量链接code
     */
    private String linkCode;

    /**
     * 附件列表
     */
    private List<PopularityStatisticalUrlItem> attachments;

    /**
     * 喜欢数量
     */
    private Integer likeNum;

    /**
     * 评论数量
     */
    private Integer commentNum;

    /**
     * 收藏数量
     */
    private Integer collectNum;

    /**
     * 转发数量
     */
    private Integer forwardNum;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 所属活动
     */
    private String ownedActivities;



    public void patch(HundredBillionImageDataDto dto) {
        this.likeNum = Math.max(dto.getLike(), this.likeNum);
        this.collectNum = Math.max(dto.getLike(), this.collectNum);
        this.commentNum = Math.max(dto.getLike(), this.commentNum);
        this.forwardNum = Math.max(dto.getLike(), this.forwardNum);
        if(CollectionUtils.isNotEmpty(dto.getTagList())) {
            this.tags = dto.getTagList();
        }
    }

    public void filterEmoji(){
        // 过滤所有emoji表情和特殊符号
        if (StringUtils.isNotBlank(this.originalText)) {
            this.originalText = this.originalText.replaceAll("[\\ud800\\udc00-\\udbff\\udfff]|[\\ud800-\\udbff]|[\\udc00-\\udfff]|[\\u2600-\\u27ff]", "");
        }
    }

    public boolean valid() {
        return !StringUtils.isBlank(this.enterpriseId)
                && this.userImId != null
                && !StringUtils.isBlank(this.uniqId)
                && !StringUtils.isBlank(this.channel);
    }

    public PopularityStatisticalDto() {
    }

    public String getOwnedActivities() {
        return ownedActivities;
    }

    public void setOwnedActivities(String ownedActivities) {
        this.ownedActivities = ownedActivities;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Long getUserImId() {
        return userImId;
    }

    public void setUserImId(Long userImId) {
        this.userImId = userImId;
    }

    public String getUniqId() {
        return uniqId;
    }

    public void setUniqId(String uniqId) {
        this.uniqId = uniqId;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getLinkCode() {
        return linkCode;
    }

    public void setLinkCode(String linkCode) {
        this.linkCode = linkCode;
    }

    public List<PopularityStatisticalUrlItem> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<PopularityStatisticalUrlItem> attachments) {
        this.attachments = attachments;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public Integer getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Integer collectNum) {
        this.collectNum = collectNum;
    }

    public Integer getForwardNum() {
        return forwardNum;
    }

    public void setForwardNum(Integer forwardNum) {
        this.forwardNum = forwardNum;
    }
}
