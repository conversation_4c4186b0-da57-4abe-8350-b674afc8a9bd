package com.talk51.modules.leads.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.talk51.modules.leads.dto.InfluencersStatisticalRecordDto;
import com.talk51.modules.leads.dto.PopularityStatisticalUrlItem;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 百亿计划数据记录表
 * xiangbo001
 */
public class InfluencersStatisticalRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户编号
     */
    @JSONField(name = "stu_id")
    private Long stuId;

    /**
     * 任务编号
     */
    private String task_id;

    /**
     * 消息渠道（XHS--小红书 DY--抖音 WX_VIDEO--微信视频号）
     */
    private String channel;

    /**
     * 完整流量来源文本
     */
    private String remark;


    /**
     * 流量完整链接
     */
    private String influencers_link;

    /**
     * 推文附件列表
     */
    private String attachments;

    /**
     * 喜欢数量
     */
    @JSONField(name = "like_num")
    private Integer likeNum;

    /**
     * 评论数量
     */
    @JSONField(name = "comment_num")
    private Integer commentNum;

    /**
     * 收藏数量
     */
    @JSONField(name = "collect_num")
    private Integer collectNum;

    /**
     * 转发数量
     */
    @JSONField(name = "forward_num")
    private Integer forwardNum;

    private String tags;

    @JSONField(name = "activity_id")
    private Long activityId;
    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void from(InfluencersStatisticalRecordDto dto) {
        if (dto.getId() != null) {
            this.id = dto.getId();
        }
        if (dto.getStuId() != null) {
            this.stuId = dto.getStuId();
        }
        if (dto.getTaskId() != null) {
            this.task_id = dto.getTaskId();
        }
        if (dto.getRemark() != null) {
            this.remark = dto.getRemark();
        }
        if (dto.getChannel() != null) {
            this.channel = dto.getChannel();
        }
        if (dto.getInfluencers_link() != null) {
            this.influencers_link = dto.getInfluencers_link();
        }
        if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
            this.attachments = dto.getAttachments().stream()
                    .map(PopularityStatisticalUrlItem::getUrl)
                    .collect(Collectors.joining(","));
        }
        if (dto.getLikeNum() > 0) {
            this.likeNum = dto.getLikeNum();
        }
        if (dto.getCommentNum() > 0) {
            this.commentNum = dto.getCommentNum();
        }
        if (dto.getCollectNum() > 0) {
            this.collectNum = dto.getCollectNum();
        }
        if (dto.getForwardNum() > 0) {
            this.forwardNum = dto.getForwardNum();
        }
        if (CollectionUtils.isNotEmpty(dto.getTags())) {
            this.tags = String.join(",", dto.getTags());
        }
        if(dto.getActivityId() != null) {
            this.activityId = dto.getActivityId();
        }
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public Integer getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Integer collectNum) {
        this.collectNum = collectNum;
    }

    public Integer getForwardNum() {
        return forwardNum;
    }

    public void setForwardNum(Integer forwardNum) {
        this.forwardNum = forwardNum;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getStuId() {
        return stuId;
    }

    public void setStuId(Long stuId) {
        this.stuId = stuId;
    }

    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInfluencers_link() {
        return influencers_link;
    }

    public void setInfluencers_link(String influencers_link) {
        this.influencers_link = influencers_link;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
}
