package com.talk51.modules.leads.dto;

import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

public class InfluencersStatisticalRecordDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户编号
     */
    @JSONField(name = "stu_id")
    private Long stuId;

    /**
     * 流量唯一编号
     */
    @JSONField(name = "task_id")
    private String taskId;

    /**
     * 消息渠道（XHS--小红书 DY--抖音 WX_VIDEO--微信视频号）
     */
    private String channel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流量完整链接
     */
    private String influencers_link;

    /**
     * 附件列表
     */
    private List<PopularityStatisticalUrlItem> attachments;

    /**
     * 喜欢数量
     */
    private Integer likeNum;

    /**
     * 评论数量
     */
    private Integer commentNum;

    /**
     * 收藏数量
     */
    private Integer collectNum;

    /**
     * 转发数量
     */
    private Integer forwardNum;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 所属活动
     */
    @JSONField(name = "activity_id")
    private Long activityId;



    public void patch(InfluencersImageDataDto dto) {
        this.likeNum = Math.max(dto.getLike(), this.likeNum);
        this.collectNum = Math.max(dto.getLike(), this.collectNum);
        this.commentNum = Math.max(dto.getLike(), this.commentNum);
        this.forwardNum = Math.max(dto.getLike(), this.forwardNum);
        if(CollectionUtils.isNotEmpty(dto.getTagList())) {
            this.tags = dto.getTagList();
        }
    }

    public void filterEmoji(){
        // 过滤所有emoji表情和特殊符号
        if (StringUtils.isNotBlank(this.remark)) {
            this.remark = this.remark.replaceAll("[\\ud800\\udc00-\\udbff\\udfff]|[\\ud800-\\udbff]|[\\udc00-\\udfff]|[\\u2600-\\u27ff]", "");
        }
    }

    public boolean valid() {
        return this.stuId != null
                && !StringUtils.isBlank(this.taskId)
                && !StringUtils.isBlank(this.channel);
    }

    public InfluencersStatisticalRecordDto() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }



    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }



    public List<PopularityStatisticalUrlItem> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<PopularityStatisticalUrlItem> attachments) {
        this.attachments = attachments;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public Integer getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Integer collectNum) {
        this.collectNum = collectNum;
    }

    public Integer getForwardNum() {
        return forwardNum;
    }

    public void setForwardNum(Integer forwardNum) {
        this.forwardNum = forwardNum;
    }

    public Long getStuId() {
        return stuId;
    }

    public void setStuId(Long stuId) {
        this.stuId = stuId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInfluencers_link() {
        return influencers_link;
    }

    public void setInfluencers_link(String influencers_link) {
        this.influencers_link = influencers_link;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
}
