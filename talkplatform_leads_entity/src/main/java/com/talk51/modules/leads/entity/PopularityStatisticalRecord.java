package com.talk51.modules.leads.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.talk51.modules.leads.dto.InfluencersStatisticalDto;
import com.talk51.modules.leads.dto.PopularityStatisticalUrlItem;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 百亿计划数据记录表
 * xiangbo001
 */
public class PopularityStatisticalRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 企业微信机器人编号
     */
    @JSONField(name = "enterprise_id")
    private String enterpriseId;

    /**
     * 用户编号
     */
    @JSONField(name = "user_im_id")
    private Long userImId;

    /**
     * 流量唯一编号
     */
    private String uniq_id;

    /**
     * 完整流量来源文本
     */
    @JSONField(name = "original_text")
    private String originalText;

    /**
     * 消息渠道（XHS--小红书 DY--抖音 WX_VIDEO--微信视频号）
     */
    private String channel;

    /**
     * 流量完整链接
     */
    private String link;

    /**
     * 流量完整链接
     */
    @JSONField(name = "link_code")
    private String linkCode;

    /**
     * 推文附件列表
     */
    private String attachments;

    /**
     * 喜欢数量
     */
    @JSONField(name = "like_num")
    private Integer likeNum;

    /**
     * 评论数量
     */
    @JSONField(name = "comment_num")
    private Integer commentNum;

    /**
     * 收藏数量
     */
    @JSONField(name = "collect_num")
    private Integer collectNum;

    /**
     * 转发数量
     */
    @JSONField(name = "forward_num")
    private Integer forwardNum;

    private String tags;

    @JSONField(name = "owned_activities")
    private String ownedActivities;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void from(InfluencersStatisticalDto dto) {
        if (dto.getId() != null) {
            this.id = dto.getId();
        }
        if (dto.getEnterpriseId() != null) {
            this.enterpriseId = dto.getEnterpriseId();
        }
        if (dto.getUserImId() != null) {
            this.userImId = dto.getUserImId();
        }
        if (dto.getUniqId() != null) {
            this.uniq_id = dto.getUniqId();
        }
        if (dto.getOriginalText() != null) {
            this.originalText = dto.getOriginalText();
        }
        if (dto.getChannel() != null) {
            this.channel = dto.getChannel();
        }
        if (dto.getLink() != null) {
            this.link = dto.getLink();
        }
        if (dto.getLinkCode() != null) {
            this.linkCode = dto.getLinkCode();
        }
        if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
            this.attachments = dto.getAttachments().stream()
                    .map(PopularityStatisticalUrlItem::getUrl)
                    .collect(Collectors.joining(","));
        }
        if (dto.getLikeNum() > 0) {
            this.likeNum = dto.getLikeNum();
        }
        if (dto.getCommentNum() > 0) {
            this.commentNum = dto.getCommentNum();
        }
        if (dto.getCollectNum() > 0) {
            this.collectNum = dto.getCollectNum();
        }
        if (dto.getForwardNum() > 0) {
            this.forwardNum = dto.getForwardNum();
        }
        if (CollectionUtils.isNotEmpty(dto.getTags())) {
            this.tags = String.join(",", dto.getTags());
        }
        if(dto.getOwnedActivities() != null) {
            this.ownedActivities = dto.getOwnedActivities();
        }
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getOwnedActivities() {
        return ownedActivities;
    }

    public void setOwnedActivities(String ownedActivities) {
        this.ownedActivities = ownedActivities;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Long getUserImId() {
        return userImId;
    }

    public void setUserImId(Long userImId) {
        this.userImId = userImId;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public String getUniq_id() {
        return uniq_id;
    }

    public void setUniq_id(String uniq_id) {
        this.uniq_id = uniq_id;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getLinkCode() {
        return linkCode;
    }

    public void setLinkCode(String linkCode) {
        this.linkCode = linkCode;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public Integer getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Integer collectNum) {
        this.collectNum = collectNum;
    }

    public Integer getForwardNum() {
        return forwardNum;
    }

    public void setForwardNum(Integer forwardNum) {
        this.forwardNum = forwardNum;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
