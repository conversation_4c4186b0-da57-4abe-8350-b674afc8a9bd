package com.talk51.modules.leads.dto;

import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class HundredBillionImageDataDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer like;

    private Integer collect;

    private Integer comment;

    private Integer forward;

    private List<String> tagList;

    public HundredBillionImageDataDto() {
        like = 0;
        collect = 0;
        comment = 0;
        forward = 0;
        tagList = new ArrayList<>();
    }

    public void patch(HundredBillionImageDataDto dto) {
        if(Objects.isNull(dto)) {
            return;
        }
        this.like = Math.max(dto.like, this.like);
        this.collect = Math.max(dto.collect, this.collect);
        this.comment = Math.max(dto.comment, this.comment);
        this.forward = Math.max(dto.forward, this.forward);
        if(CollectionUtils.isNotEmpty(dto.tagList)) {
            this.tagList = dto.tagList;
        }
    }

    public Integer getLike() {
        return like;
    }

    public void setLike(Integer like) {
        this.like = like;
    }

    public Integer getCollect() {
        return collect;
    }

    public void setCollect(Integer collect) {
        this.collect = collect;
    }

    public Integer getComment() {
        return comment;
    }

    public void setComment(Integer comment) {
        this.comment = comment;
    }

    public Integer getForward() {
        return forward;
    }

    public void setForward(Integer forward) {
        this.forward = forward;
    }

    public List<String> getTagList() {
        return tagList;
    }

    public void setTagList(List<String> tagList) {
        this.tagList = tagList;
    }
}
