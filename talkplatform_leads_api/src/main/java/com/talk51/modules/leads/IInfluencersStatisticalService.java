package com.talk51.modules.leads;

import com.talk51.modules.leads.dto.InfluencersStatisticalRecordDto;

/**
 * <AUTHOR>
 */
public interface IInfluencersStatisticalService {

    /**
     * 处理流量人气信息
     * @param influencersStatisticalRecordDto
     */
    void influencersStatisticalInfoHandle(InfluencersStatisticalRecordDto influencersStatisticalRecordDto);

    /**
     * 处理流量人气数据
     * @param influencersStatisticalRecordDto
     */
    void influencersStatisticalDataHandle(InfluencersStatisticalRecordDto influencersStatisticalRecordDto);

    /**
     * 轮询任务发送指令给RPA
     */
    void rpaCommandTask();
}
