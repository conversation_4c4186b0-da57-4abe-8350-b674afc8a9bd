import com.alibaba.fastjson.JSON;
import com.talk51.common.utils.DateUtils;
import com.talk51.modules.leads.*;
import com.talk51.modules.leads.dto.*;
import com.talk51.modules.leads.entity.MerchantsInfo;
import com.talk51.modules.leads.entity.ThirdOrderDetail;
import com.talk51.modules.leads.enums.ThirdOrderProcessStatusEnum;
import com.talk51.modules.leads.performance.IAnchorPerformanceRecordService;
import com.talk51.modules.leads.service.order.action.dispatcher.OrderDispatcherContext;
import com.talk51.modules.leads.service.order.builder.ThirdLeadsHttpBuilder;
import com.talk51.modules.leads.service.order.builder.TiktokBuilder;
import com.talk51.modules.leads.service.order.facade.ThirdOrderFacade;
import com.talk51.modules.leads.task.TryConsumerTask;
import com.talk51.modules.leads.vo.TiktokPOrderVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"classpath*:/spring-context*.xml"})
public class ServiceTest {

    public static final String appID = "7160569481616426507";
    public static final String secret = "5b63dfde-b388-45e3-8b43-25db0dfad701";
    public static final String accessToken = "05540e58-7926-4e77-9620-1b1f36c0ccd6";

    @Autowired
    private IThirdTokenHandleService thirdTokenHandleService;

    @Autowired
    private ThirdOrderFacade thirdOrderFacade;

    @Autowired
    private TiktokBuilder tiktokBuilder;

    @Autowired
    private IThirdOrderService thirdOrderService;

    @Autowired
    private IThirdOrderDetailService thirdOrderDetailService;

    @Autowired
    private IThirdProductService thirdProductService;

    @Autowired
    private ThirdLeadsHttpBuilder thirdLeadsHttpBuilder;

    @Autowired
    private OrderDispatcherContext orderDispatcherContext;

    @Autowired
    private IAnchorPerformanceRecordService anchorPerformanceRecordService;

    @Autowired
    private TryConsumerTask tryConsumerTask;

    @Autowired
    private IInfluencersStatisticalService hundredBillionService;

    @Test
    public void tryConsumerTaskAgain(){
        tryConsumerTask.executeByOrder(6919302847124936267L);
    }

    @Test
    public void testToken(){
        //63b32567-7eb0-498d-b8d2-38a1c6fa274e
        //63b32567-7eb0-498d-b8d2-38a1c6fa274e
        String accessToken = thirdTokenHandleService.getToken("douyin",53648837L);
        System.out.println(accessToken);
    }

    public void testGetTokenResetCache(){
        String accessToken = thirdTokenHandleService.getToken("douyin",0L);
        System.out.println(accessToken);
    }

    @Test
    public void orderDetailTest() throws Exception{
        MerchantsInfo merchantsInfo = new MerchantsInfo();
        merchantsInfo.setAppId(appID);
        merchantsInfo.setSecret(secret);
        merchantsInfo.setShopId(0L);
        TiktokPOrderVo orderDetail = thirdOrderFacade.queryOrderDetail(accessToken,"4995811383561985216",merchantsInfo);
        System.out.println(JSON.toJSONString(orderDetail));
    }

    @Test
    public void orderBatchDecrypt() throws Exception{
        ThirdOrderDto thirdOrderDto = new ThirdOrderDto();
        thirdOrderDto.setPlatformOrderId("4843507183174489486");
        thirdOrderDto.setMobile("$dp4/OKaxLeTef0z8Bc28UPGQWJqln7ijQuVGgEFVH6c=$rCwCUqy6ezLhVUf0S5MA4nl6stCrAOT6/ZNDxNAQVhgGtX+OoDoWmAgI/tOx25HcFlS3HOp59ZWeSv4AMoyjhpNy3mO5Ym45UCG9Br2z2cho3g==*CgkIARCtHCABKAESPgo8F7rq/Xtga+yy6uGs4bjmQDDNBBSFMi98kAen6IGoRBHzenfFlfD5eVRpdzc6wtwLeYCq2ENsFyqTjU+pGgA=$1$$");
        thirdOrderDto.setBuyerNick("#LaY7#LIC0lJi0TE+beO+/6EIOpOXZK3DsETrUi9pUKJOUQCieYTGMBJtn5vGKporZf7tbBncO9P2VmaLRjLprYDUmpctw1oMiPyo5wZZiysY=*CgkIARCtHCABKAESPgo8hBstZ+/XDE/o5MAvoTKfJCcKD/xlRj72YOTdRe8QMP+6fpsqdEL0mWUODB1qDnZgORXcgBNnCGFhhWWXGgA=#1##");
        thirdOrderDto.setMsgId("68498447138170534840::101:1631812152:6953067346286118415");
//        TiktokDecryptResultDto decryptResultDto = tiktokBuilder.tiktokDataDecrypt(appID, secret, accessToken, thirdOrderDto.getPlatformOrderId(), thirdOrderDto.getMobile(), thirdOrderDto.getBuyerNick(), thirdOrderDto.getMsgId());
//        System.out.println(JSON.toJSONString(decryptResultDto));
    }

    @Test
    public void againConsumer() throws Exception{
        thirdOrderService.againConsumer("36268863368335021050:0:100:1671069738:640905938:7160569481616426507","douyin");
    }

    @Test
    public void addEquityTray(){
        List<ThirdOrderDetail> list = thirdOrderDetailService.queryThirdOrderDetailListByStep(Arrays.asList("5001788444718710101"), ThirdOrderProcessStatusEnum.ORDER_ADD_EQUITY_SUCCESS.getCode());
        ThirdOrderPaySuccessDto paySuccessDto = new ThirdOrderPaySuccessDto();
        paySuccessDto.setUserId(57542892L);
        paySuccessDto.setMerchantsCode("douyin");
        paySuccessDto.setMsgId("10101781744488710050:0:101:1668665057:1444351682:7160569481616426507");
        orderDispatcherContext.orderPaySuccessAddEquity(paySuccessDto,list.get(0));
    }

    @Test
    public void delEquityTray(){
        List<ThirdOrderDetail> list = thirdOrderDetailService.queryThirdOrderDetailListByStep(Arrays.asList("4999573040473208097"), ThirdOrderProcessStatusEnum.REFUNDED.getCode());
        ThirdOrderRefundDto refundDto = new ThirdOrderRefundDto();
        refundDto.setUserId(32141529L);
        refundDto.setMerchantsCode("douyin");
        refundDto.setMsgId("79080237404037599940:0:206:1668155548:3059999522:7160569481616426507");
        orderDispatcherContext.orderRefundSuccessDelEquity(refundDto,list.get(0));
    }

    @Test
    public void tryTaskConsumer() throws Exception{
        Date now = new Date();
        now = DateUtils.addDays(now,-1);
        thirdOrderService.taskTryConsumer(now);
        while (true){
            Thread.sleep(10000);
        }
    }

    @Test
    public void productDetailTest() throws Exception{
        System.out.println(JSON.toJSONString(thirdProductService.queryDouyinProductDetail("3558732588271177951",53823933L)));
        System.out.println(JSON.toJSONString(thirdProductService.queryDouyinProductDetail("3587536990956068742",53648837L)));
    }

    @Test
    public void isBuy() throws Exception{
        Boolean isBuy = thirdLeadsHttpBuilder.isBuy(6613124213213L);
        System.out.println(isBuy);
    }

    @Test
    public void addWhite() throws Exception{
        for(String userId:"57541610,57541439,45551208,57539535,57540773,56334743,57539253,57540124,49132873,57528602,44524966,34242458,46930787,57539151,57539131,45551698,51169351,47122373,57538838,57517880,55135312,51840922,57537926".split(",")){
            thirdLeadsHttpBuilder.addRecordedWhite(Long.parseLong(userId),"1293");
        }
    }

    @Test
    public void delWhite() throws Exception{
        thirdLeadsHttpBuilder.delRecordedWhite(36640902L,"7705");
    }

    @Test
    public void addAiCourse() throws Exception{
        String userOrder[] = "55135312,30219791#30236213,30220014#51840922,30219812#57537745,30219364".split("#");
        for(String line:userOrder){
            String arr[] = line.split(",");
            thirdLeadsHttpBuilder.addAiCourse(Long.parseLong(arr[0]),Integer.parseInt(arr[1]),"hm_abc");
        }

    }

    @Test
    public void delAiCourse() throws Exception{
        thirdLeadsHttpBuilder.delAiCourse(36640902L,1,"hm_abc");
    }
    @Test
    public void queryAnchorPerformance() throws Exception{
        anchorPerformanceRecordService.processAnchorPerformance(DateUtils.parseDate("2023-02-27"),DateUtils.parseDate("2023-03-10"));
        while (true){
            Thread.sleep(10000L);
        }
    }

    @Test
    public void hundredBillionPlanMsgTest() {
        HundredBillionMsgParam param = new HundredBillionMsgParam();
        param.setMsg_id("123456");
        param.setAction("MESSAGE");
        param.setType("AI_CONTENT_FETCHER");
        param.setChannel("WORK_WX");
        param.setProvider("TALK51");
        param.setEnterprise_im_id("abc");
        param.setUser_im_id("0001");
        param.setText("43 博文.发布了一篇小红书笔记，快来看吧！ \uD83D\uDE06 sZgFkVyBkBXimVd \uD83D\uDE06 http://xhslink.com/a/7lrl9fkzguZcb，复制本条信息，打开【小红书】App查看精彩内容！");
//        hundredBillionService.popularityStatisticalMesHandle(param);
    }

    @Test
    public void hundredBillionPlanAttachmentTest() {
        HundredBillionMsgParam param = new HundredBillionMsgParam();
        param.setMsg_id("100001");
        param.setAction("MEDIA_FETCH");
        param.setType("AI_CONTENT_FETCHER");
        param.setChannel("WHATSAPP");
        param.setProvider("TALK51");
        param.setEnterprise_im_id("abc");
        param.setUser_im_id("0001");
        param.setText("{\n" +
                "    \"user_im_id\": \"1\",\n" +
                "    \"url\": \"http://xhslink.com/a/7lrl9fkzguZcb\",\n" +
                "    \"result\": [{\n" +
                "        \"sort\": 1,\n" +
                "        \"url\": \"https://iknow-pic.cdn.bcebos.com/d058ccbf6c81800a6616a689a33533fa838b4741?x-bce-process=image%2Fresize%2Cm_lfit%2Cw_600%2Ch_800%2Climit_1%2Fquality%2Cq_85%2Fformat%2Cf_auto\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }");
        hundredBillionService.popularityStatisticalAttachmentHandle(param);
    }
}
