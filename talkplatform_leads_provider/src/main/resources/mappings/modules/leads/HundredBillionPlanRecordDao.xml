<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.leads.dao.HundredBillionPlanRecordDao">
    <resultMap id="BaseResultMap" type="com.talk51.modules.leads.entity.InfluencersStatisticalRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="enterprise_im_id" jdbcType="VARCHAR" property="enterpriseImId" />
        <result column="user_im_id" jdbcType="BIGINT" property="userImId" />
        <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
        <result column="channel" jdbcType="VARCHAR" property="channel" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="attachment" jdbcType="VARCHAR" property="attachment" />
        <result column="like_num" jdbcType="INTEGER" property="likeNum" />
        <result column="comment_num" jdbcType="INTEGER" property="commentNum" />
        <result column="collect" jdbcType="INTEGER" property="collect" />
        <result column="forward" jdbcType="INTEGER" property="forward" />
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, enterprise_im_id, user_im_id, msg_id, channel, url, attachment, like_num, comment_num,
    collect, forward, add_time, update_time
    </sql>

    <select id="queryByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.talk51.modules.leads.entity.InfluencersStatisticalRecord" useGeneratedKeys="true">
        insert into hundred_billion_plan_record (enterprise_im_id, user_im_id, msg_id, channel,
                                                 url, attachment, like_num, comment_num,
                                                 collect, forward, add_time, update_time)
        values (#{enterpriseImId,jdbcType=VARCHAR}, #{userImId,jdbcType=BIGINT}, #{msgId,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR},
                #{url,jdbcType=VARCHAR}, #{attachment,jdbcType=VARCHAR}, #{likeNum,jdbcType=INTEGER}, #{commentNum,jdbcType=INTEGER},
                #{collect,jdbcType=INTEGER}, #{forward,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.talk51.modules.leads.entity.InfluencersStatisticalRecord" useGeneratedKeys="true">
        insert into hundred_billion_plan_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseImId != null">
                enterprise_im_id,
            </if>
            <if test="userImId != null">
                user_im_id,
            </if>
            <if test="msgId != null">
                msg_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="attachment != null">
                attachment,
            </if>
            <if test="likeNum != null">
                like_num,
            </if>
            <if test="commentNum != null">
                comment_num,
            </if>
            <if test="collect != null">
                collect,
            </if>
            <if test="forward != null">
                forward,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseImId != null">
                #{enterpriseImId,jdbcType=VARCHAR},
            </if>
            <if test="userImId != null">
                #{userImId,jdbcType=BIGINT},
            </if>
            <if test="msgId != null">
                #{msgId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="attachment != null">
                #{attachment,jdbcType=VARCHAR},
            </if>
            <if test="likeNum != null">
                #{likeNum,jdbcType=INTEGER},
            </if>
            <if test="commentNum != null">
                #{commentNum,jdbcType=INTEGER},
            </if>
            <if test="collect != null">
                #{collect,jdbcType=INTEGER},
            </if>
            <if test="forward != null">
                #{forward,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.leads.entity.InfluencersStatisticalRecord">
        update hundred_billion_plan_record
        <set>
            <if test="enterpriseImId != null">
                enterprise_im_id = #{enterpriseImId,jdbcType=VARCHAR},
            </if>
            <if test="userImId != null">
                user_im_id = #{userImId,jdbcType=BIGINT},
            </if>
            <if test="msgId != null">
                msg_id = #{msgId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="attachment != null">
                attachment = #{attachment,jdbcType=VARCHAR},
            </if>
            <if test="likeNum != null">
                like_num = #{likeNum,jdbcType=INTEGER},
            </if>
            <if test="commentNum != null">
                comment_num = #{commentNum,jdbcType=INTEGER},
            </if>
            <if test="collect != null">
                collect = #{collect,jdbcType=INTEGER},
            </if>
            <if test="forward != null">
                forward = #{forward,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.talk51.modules.leads.entity.InfluencersStatisticalRecord">
        update hundred_billion_plan_record
        set enterprise_im_id = #{enterpriseImId,jdbcType=VARCHAR},
            user_im_id = #{userImId,jdbcType=BIGINT},
            msg_id = #{msgId,jdbcType=VARCHAR},
            channel = #{channel,jdbcType=VARCHAR},
            url = #{url,jdbcType=VARCHAR},
            attachment = #{attachment,jdbcType=VARCHAR},
            like_num = #{likeNum,jdbcType=INTEGER},
            comment_num = #{commentNum,jdbcType=INTEGER},
            collect = #{collect,jdbcType=INTEGER},
            forward = #{forward,jdbcType=INTEGER},
            add_time = #{addTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryByUserImId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where user_im_id = #{userImId,jdbcType=BIGINT}
        order by add_time desc
    </select>

    <select id="queryByMsgId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where msg_id = #{msgId,jdbcType=VARCHAR}
    </select>

    <select id="queryByEnterpriseImId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where enterprise_im_id = #{enterpriseImId,jdbcType=VARCHAR}
        order by add_time desc
    </select>

    <select id="queryByChannel" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where channel = #{channel,jdbcType=VARCHAR}
        order by add_time desc
    </select>

    <select id="queryByUrl" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where url = #{url,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="queryByTimeRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where add_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        order by add_time desc
    </select>

    <select id="queryByUserAndChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hundred_billion_plan_record
        where user_im_id = #{userImId,jdbcType=BIGINT} and channel = #{channel,jdbcType=VARCHAR}
        order by add_time desc
    </select>

    <update id="updateStatistics">
        update hundred_billion_plan_record
        set like_num = #{likeNum,jdbcType=INTEGER},
            comment_num = #{commentNum,jdbcType=INTEGER},
            collect = #{collect,jdbcType=INTEGER},
            forward = #{forward,jdbcType=INTEGER},
            update_time = now()
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
