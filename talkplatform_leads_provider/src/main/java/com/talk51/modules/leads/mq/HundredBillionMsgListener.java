package com.talk51.modules.leads.mq;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.modules.leads.IInfluencersStatisticalService;
import com.talk51.modules.leads.dto.HundredBillionMsgParam;
import com.talk51.modules.leads.dto.InfluencersStatisticalRecordDto;
import com.talk51.modules.leads.enums.PopularityChannelEnum;
import com.talk51.modules.leads.utils.DeadQueueUtils;
import com.talk51.modules.leads.utils.RouteSendMailUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HundredBillionMsgListener implements ChannelAwareMessageListener {

    private final Logger logger = LoggerFactory.getLogger(HundredBillionMsgListener.class);

    @Autowired
    private IInfluencersStatisticalService popularityStatisticalService;

    public static String regex = "https?://(?:xhslink\\.com/a|v\\.douyin\\.com)/\\w+";
    public static Pattern pattern = Pattern.compile(regex);

    @Value("${hundred.billion.name}")
    private String hundredBillionName;
    @Value("${hundred.billion.msg.type}")
    private String hundredBillionMsgType;
    @Value("${hundred.billion.msg.action}")
    private String hundredBillionMsgAction;
    @Value("${hundred.billion.msg.channel}")
    private String hundredBillionMsgChannel;
    @Value("${hundred.billion.msg.provider}")
    private String hundredBillionMsgProvider;
    @Value("#{'${hundred.billion.msg.enterprise-im-id}'.split(',')}")
    private List<String> hundredBillionMsgEnterpriseImId;

    /**
     * 千亿计划接收RPA系统的微信聊天消息
     */
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = null;
        try {
            body = new String(message.getBody(), StandardCharsets.UTF_8);
            logger.info("【千亿计划-消息】消息接收：{}", body);
            HundredBillionMsgParam hundredBillionMsgParam = JSONObject.parseObject(JSONObject.parseObject(body).toJSONString(),
                    HundredBillionMsgParam.class);
            if (hundredBillionMsgParam != null) {
                String messageBody = checkMsgBody(hundredBillionMsgParam);
                if (StringUtils.isBlank(messageBody)) {
                    logger.error("消息解析失败: {}", JSONObject.toJSONString(hundredBillionMsgParam));
                    return;
                }
                //处理消息
                InfluencersStatisticalRecordDto influencersStatisticalRecordDto = messageConvertDto(hundredBillionMsgParam);
                //解析源文本 分析渠道
                if (!analysisChannel(influencersStatisticalRecordDto, messageBody)) {
                    return;
                }
                popularityStatisticalService.popularityStatisticalInfoHandle(influencersStatisticalRecordDto);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        } catch (Exception ex) {
            if (DeadQueueUtils.isFiveDeadMessage(message)) {
                //如果消费未成功，且message在死信队列里循环5次，则不再处理 mail发出告警
                RouteSendMailUtil.sendMailEx(ex);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                logger.error("处理mq中event发生异常 ,json ==>{}", body, ex);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            }
        }
    }

    private boolean analysisChannel(InfluencersStatisticalRecordDto dto, String message) {
        Matcher matcher = pattern.matcher(message);
        String fullUrl = "";

        //解析微信消息中的链接
        try {
            while (matcher.find()) {
                fullUrl = matcher.group(0); // 完整网址
                if (fullUrl.startsWith("https://v.douyin.com")) {
                    dto.setChannel(PopularityChannelEnum.DY.name());
                } else if (fullUrl.startsWith("http://xhslink.com")) {
                    dto.setChannel(PopularityChannelEnum.XHS.name());
                } else {
                    return false;
                }
                logger.info("完整网址: {} ", fullUrl);
            }
        } catch (Exception e) {
            logger.error("解析网址失败 message:{}", message, e);
            return false;
        }
        dto.setLink(fullUrl);
        return true;
    }

    private InfluencersStatisticalRecordDto messageConvertDto(HundredBillionMsgParam hundredBillionMsgParam) {
        InfluencersStatisticalRecordDto dto = new InfluencersStatisticalRecordDto();
        dto.setEnterpriseId(hundredBillionMsgParam.getEnterprise_im_id());
        dto.setUserImId(Long.valueOf(hundredBillionMsgParam.getUser_im_id()));
        dto.setUniqId(hundredBillionMsgParam.getMsg_id());
        dto.setChannel(hundredBillionMsgParam.getChannel());
        dto.setOriginalText(hundredBillionMsgParam.getText());
        dto.setChannel(hundredBillionMsgParam.getChannel());
        dto.setOwnedActivities(hundredBillionName);
        return dto;
    }


    /**
     * 过滤消息是否合规
     */
    private String checkMsgBody(HundredBillionMsgParam param) {
        String message = "";
        if (Objects.isNull(param)
                && hundredBillionMsgType.equals(param.getType())
                && hundredBillionMsgAction.equals(param.getAction())
                && hundredBillionMsgChannel.equals(param.getChannel())
                && hundredBillionMsgProvider.equals(param.getProvider())
                && hundredBillionMsgEnterpriseImId.contains(param.getEnterprise_im_id())) {
            JSONObject messageObject = JSONObject.parseObject(param.getText());
            message = messageObject.getString("message");
        }
        return message;
    }

}
