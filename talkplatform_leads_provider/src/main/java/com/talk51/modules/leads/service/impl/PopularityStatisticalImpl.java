package com.talk51.modules.leads.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.leads.IInfluencersStatisticalService;
import com.talk51.modules.leads.dto.*;
import com.talk51.modules.leads.entity.InfluencersStatisticalRecord;
import com.talk51.modules.leads.enums.PopularityChannelEnum;
import com.talk51.modules.leads.utils.HttpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("popularityStatisticalService")
public class PopularityStatisticalImpl implements IInfluencersStatisticalService {

    private static final Logger logger = LoggerFactory.getLogger(PopularityStatisticalImpl.class);

    @Value("${hundred.billion.media.type}")
    private String hundredBillionMediaType;
    @Value("${hundred.billion.media.action}")
    private String hundredBillionMediaAction;
    @Value("${hundred.billion.media.channel}")
    private String hundredBillionMediaChannel;
    @Value("${hundred.billion.media.provider}")
    private String hundredBillionMediaProvider;
    @Value("#{'${hundred.billion.media.enterprise-im-id}'.split(',')}")
    private List<String> hundredBillionMediaEnterpriseImId;
    @Value("${hundred.billion.media.dify.url}")
    private String hundredBillionMediaDifyUrl;
    @Value("${hundred.billion.media.dify.acesstoken}")
    private String hundredBillionMediaDifyAcesstoken;
    @Value("${hundred.billion.media.dify.user}")
    private String hundredBillionMediaDifyUser;


    @Override
    public void influencersStatisticalInfoHandle(InfluencersStatisticalRecordDto influencersStatisticalRecordDto) {
        // 校验DTO是否合规
        if (influencersStatisticalRecordDto.valid()) {
            return;
        }
        //todo 检查有没有重复的数据
        if (checkRepeatInfo(influencersStatisticalRecordDto)) {
            logger.error("消息已存在 不重复入库:{}", JSONObject.toJSONString(influencersStatisticalRecordDto));
            return;
        }
        //todo 根据唯一链接生成唯一code标识

        //todo 信息入库
    }

    @Override
    public void influencersStatisticalDataHandle(InfluencersStatisticalRecordDto influencersStatisticalRecordDto) {
        // 校验DTO是否合规
        if (influencersStatisticalRecordDto.valid()) {
            return;
        }

        if (CollectionUtils.isNotEmpty(influencersStatisticalRecordDto.getAttachments())) {
            // 有附件 则取出数据解析图片
            influencersStatisticalRecordDto.getAttachments().forEach(attachment -> {
                sendImageAnalysisRequest(influencersStatisticalRecordDto, attachment.getUrl());
            });
            // 保存流水记录
            InfluencersStatisticalRecord record = new InfluencersStatisticalRecord();
            record.from(influencersStatisticalRecordDto);
            //todo 从数据库取出该条统计数据

            //todo 统计数据数据落库
        }
    }

    @Override
    public void rpaCommandTask() {
        Arrays.stream(PopularityChannelEnum.values()).forEach(e -> {
            String channel = e.name();
        });
    }

    private boolean checkRepeatInfo(InfluencersStatisticalRecordDto influencersStatisticalRecordDto) {
        return true;
    }

    private void sendImageAnalysisRequest(InfluencersStatisticalRecordDto dto, String url) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + hundredBillionMediaDifyAcesstoken);

        // 请求dify大模型 获取流量数据
        List<DifyFileDto> difyFiles = new ArrayList<>();
        DifyFileDto difyFileDto = new DifyFileDto("image", "remote_url", url);
        difyFiles.add(difyFileDto);
        HundredBillionDifyRequestParam param = new HundredBillionDifyRequestParam();
        param.setQuery("帮我识别这张图片");
        param.setUser(hundredBillionMediaDifyUser);
        param.setFiles(difyFiles);
        String requestBody = JSONObject.toJSONString(param);
        InfluencersImageDataDto imageDataDto;
        try {
            String response = HttpUtils.postJson(hundredBillionMediaDifyUrl, requestBody, headers);
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.containsKey("answer")) {
                String answerJson = jsonObject.getString("answer");
                imageDataDto = JSONObject.parseObject(answerJson, InfluencersImageDataDto.class);
                dto.patch(imageDataDto);
            }
        } catch (HttpUtils.HttpRequestException e) {
            logger.error("【人气流量统计】 请求dify平台报错 :{}", url, e);
        }
    }

}
