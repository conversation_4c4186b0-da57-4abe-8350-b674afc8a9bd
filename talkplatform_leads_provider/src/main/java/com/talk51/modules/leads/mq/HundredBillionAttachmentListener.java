package com.talk51.modules.leads.mq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.modules.leads.IInfluencersStatisticalService;
import com.talk51.modules.leads.dto.HundredBillionMsgParam;
import com.talk51.modules.leads.utils.DeadQueueUtils;
import com.talk51.modules.leads.utils.RouteSendMailUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

public class HundredBillionAttachmentListener implements ChannelAwareMessageListener {

    private final Logger logger = LoggerFactory.getLogger(HundredBillionAttachmentListener.class);

    @Autowired
    private IInfluencersStatisticalService hundredBillionService;

    /**
     * 千亿计划接收RPA系统的截图附件消息
     *
     */
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = null;
        try {
            body = new String(message.getBody(), StandardCharsets.UTF_8);
            logger.info("【千亿计划-附件】消息接收：" + body);
            HundredBillionMsgParam hundredBillionMsgParam = JSONObject.parseObject(JSONObject.parseObject(body).toJSONString(),
                    HundredBillionMsgParam.class);
            if (hundredBillionMsgParam != null) {
                //处理消息
                hundredBillionService.popularityStatisticalAttachmentHandle(hundredBillionMsgParam);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        } catch (Exception ex) {
            if (DeadQueueUtils.isFiveDeadMessage(message)) {
                //如果消费未成功，且message在死信队列里循环5次，则不再处理 mail发出告警
                RouteSendMailUtil.sendMailEx(ex);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                logger.error("处理mq中event发生异常 ,json ==>{}", body, ex);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            }
        }
    }
}
