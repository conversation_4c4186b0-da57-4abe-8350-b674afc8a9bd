package com.talk51.modules.leads.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.leads.entity.InfluencersStatisticalRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * HundredBillionPlanRecordDao继承基类
 * <AUTHOR>
 */
@MyBatisDao
public interface HundredBillionPlanRecordDao {

    int insert(InfluencersStatisticalRecord influencersStatisticalRecord);

    int insertSelective(InfluencersStatisticalRecord influencersStatisticalRecord);

    InfluencersStatisticalRecord queryByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfluencersStatisticalRecord influencersStatisticalRecord);

    int updateByPrimaryKey(InfluencersStatisticalRecord influencersStatisticalRecord);

    /**
     * 根据用户IM ID查询记录列表
     * @param userImId
     * @return
     */
    List<InfluencersStatisticalRecord> queryByUserImId(@Param("userImId") Long userImId);

    /**
     * 根据消息ID查询记录
     * @param msgId
     * @return
     */
    InfluencersStatisticalRecord queryByMsgId(@Param("msgId") String msgId);

    /**
     * 根据企业ID查询记录列表
     * @param enterpriseImId
     * @return
     */
    List<InfluencersStatisticalRecord> queryByEnterpriseImId(@Param("enterpriseImId") String enterpriseImId);

    /**
     * 根据渠道查询记录列表
     * @param channel
     * @return
     */
    List<InfluencersStatisticalRecord> queryByChannel(@Param("channel") String channel);

    /**
     * 根据URL查询记录
     * @param url
     * @return
     */
    InfluencersStatisticalRecord queryByUrl(@Param("url") String url);

    /**
     * 根据时间范围查询记录列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<InfluencersStatisticalRecord> queryByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据用户和渠道查询记录列表
     * @param userImId
     * @param channel
     * @return
     */
    List<InfluencersStatisticalRecord> queryByUserAndChannel(@Param("userImId") Long userImId, @Param("channel") String channel);

    /**
     * 更新统计数据（点赞、评论、收藏、转发）
     * @param id
     * @param likeNum
     * @param commentNum
     * @param collect
     * @param forward
     * @return
     */
    int updateStatistics(@Param("id") Long id, @Param("likeNum") Integer likeNum,
                        @Param("commentNum") Integer commentNum, @Param("collect") Integer collect, 
                        @Param("forward") Integer forward);
}
